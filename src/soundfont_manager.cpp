#include "soundfont_manager.h"
#include <algorithm>
#include <iostream>
#include <filesystem>
#include <cstdint>

SoundFontManager::SoundFontManager() 
    : BASS_MIDI_FontInit_ptr(nullptr), BASS_MIDI_FontFree_ptr(nullptr) {
}

SoundFontManager::~SoundFontManager() {
    UnloadAllSoundFonts();
}

bool SoundFontManager::AddSoundFont(const std::string& path) {
    if (path.empty() || HasSoundFont(path)) {
        return false;
    }
    
    if (!ValidateSoundFontPath(path)) {
        std::cerr << "Invalid soundfont path: " << path << std::endl;
        return false;
    }
    
    // Create new soundfont info with next priority
    int next_priority = soundfonts_.empty() ? 0 : soundfonts_.back().priority + 1;
    SoundFontInfo info(path, next_priority);
    
    soundfonts_.push_back(info);
    NotifyChange();
    
    std::cout << "Added soundfont: " << info.name << " (priority: " << next_priority << ")" << std::endl;
    return true;
}

bool SoundFontManager::RemoveSoundFont(const std::string& path) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return RemoveSoundFont(static_cast<size_t>(index));
    }
    return false;
}

bool SoundFontManager::RemoveSoundFont(size_t index) {
    if (index >= soundfonts_.size()) {
        return false;
    }
    
    // Unload the soundfont first
    UnloadSoundFont(index);
    
    std::string name = soundfonts_[index].name;
    soundfonts_.erase(soundfonts_.begin() + index);
    
    // Update priorities to maintain order
    UpdatePriorities();
    NotifyChange();
    
    std::cout << "Removed soundfont: " << name << std::endl;
    return true;
}

void SoundFontManager::ClearAllSoundFonts() {
    UnloadAllSoundFonts();
    soundfonts_.clear();
    NotifyChange();
    std::cout << "Cleared all soundfonts" << std::endl;
}

bool SoundFontManager::SetSoundFontEnabled(size_t index, bool enabled) {
    if (index >= soundfonts_.size()) {
        return false;
    }
    
    if (soundfonts_[index].enabled != enabled) {
        soundfonts_[index].enabled = enabled;
        NotifyChange();
        
        std::cout << (enabled ? "Enabled" : "Disabled") << " soundfont: " 
                  << soundfonts_[index].name << std::endl;
    }
    
    return true;
}

bool SoundFontManager::SetSoundFontEnabled(const std::string& path, bool enabled) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return SetSoundFontEnabled(static_cast<size_t>(index), enabled);
    }
    return false;
}

bool SoundFontManager::SetSoundFontVolume(size_t index, float volume) {
    if (index >= soundfonts_.size()) {
        return false;
    }

    // Clamp volume to valid range (0.0 to 1.0)
    volume = std::max(0.0f, std::min(1.0f, volume));

    if (soundfonts_[index].volume != volume) {
        soundfonts_[index].volume = volume;
        NotifyChange();

        std::cout << "Set volume for soundfont: " << soundfonts_[index].name
                  << " to " << (volume * 100.0f) << "%" << std::endl;
    }

    return true;
}

bool SoundFontManager::SetSoundFontVolume(const std::string& path, float volume) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return SetSoundFontVolume(static_cast<size_t>(index), volume);
    }
    return false;
}

float SoundFontManager::GetSoundFontVolume(size_t index) const {
    if (index >= soundfonts_.size()) {
        return 1.0f; // Default volume
    }
    return soundfonts_[index].volume;
}

float SoundFontManager::GetSoundFontVolume(const std::string& path) const {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return GetSoundFontVolume(static_cast<size_t>(index));
    }
    return 1.0f; // Default volume
}

bool SoundFontManager::SetSoundFontShouldLoad(size_t index, bool should_load) {
    if (index >= soundfonts_.size()) {
        return false;
    }

    if (soundfonts_[index].should_load != should_load) {
        soundfonts_[index].should_load = should_load;

        // If setting to false and currently loaded, unload it
        if (!should_load && soundfonts_[index].loaded) {
            UnloadSoundFont(index);
        }
        // If setting to true and not loaded, load it
        else if (should_load && !soundfonts_[index].loaded) {
            LoadSoundFont(index);
        }

        NotifyChange();

        std::cout << "Set should_load for soundfont: " << soundfonts_[index].name
                  << " to " << (should_load ? "true" : "false") << std::endl;
    }

    return true;
}

bool SoundFontManager::SetSoundFontShouldLoad(const std::string& path, bool should_load) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return SetSoundFontShouldLoad(static_cast<size_t>(index), should_load);
    }
    return false;
}

bool SoundFontManager::GetSoundFontShouldLoad(size_t index) const {
    if (index >= soundfonts_.size()) {
        return true; // Default should load
    }
    return soundfonts_[index].should_load;
}

bool SoundFontManager::GetSoundFontShouldLoad(const std::string& path) const {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return GetSoundFontShouldLoad(static_cast<size_t>(index));
    }
    return true; // Default should load
}

bool SoundFontManager::SetSoundFontPriority(size_t index, int priority) {
    if (index >= soundfonts_.size()) {
        return false;
    }
    
    soundfonts_[index].priority = priority;
    SortByPriority();
    NotifyChange();
    return true;
}

bool SoundFontManager::MoveSoundFontUp(size_t index) {
    if (index >= soundfonts_.size() || index == 0) {
        return false;
    }
    
    // Swap with previous item
    std::swap(soundfonts_[index], soundfonts_[index - 1]);
    UpdatePriorities();
    NotifyChange();
    return true;
}

bool SoundFontManager::MoveSoundFontDown(size_t index) {
    if (index >= soundfonts_.size() - 1) {
        return false;
    }
    
    // Swap with next item
    std::swap(soundfonts_[index], soundfonts_[index + 1]);
    UpdatePriorities();
    NotifyChange();
    return true;
}

void SoundFontManager::SortByPriority() {
    std::sort(soundfonts_.begin(), soundfonts_.end(), 
              [](const SoundFontInfo& a, const SoundFontInfo& b) {
                  return a.priority < b.priority;
              });
}

bool SoundFontManager::HasEnabledSoundFonts() const {
    return std::any_of(soundfonts_.begin(), soundfonts_.end(),
                       [](const SoundFontInfo& info) { return info.enabled; });
}

std::vector<const SoundFontInfo*> SoundFontManager::GetEnabledSoundFonts() const {
    std::vector<const SoundFontInfo*> enabled;
    
    for (const auto& info : soundfonts_) {
        if (info.enabled) {
            enabled.push_back(&info);
        }
    }
    
    // Sort by priority
    std::sort(enabled.begin(), enabled.end(),
              [](const SoundFontInfo* a, const SoundFontInfo* b) {
                  return a->priority < b->priority;
              });
    
    return enabled;
}

bool SoundFontManager::LoadSoundFont(size_t index) {
    if (index >= soundfonts_.size() || !BASS_MIDI_FontInit_ptr) {
        return false;
    }
    
    auto& info = soundfonts_[index];
    
    // Unload if already loaded
    if (info.handle != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(info.handle);
        info.handle = 0;
        info.loaded = false;
    }
    
    // Try to load the soundfont
    if (BASS_MIDI_FontInit_ptr) {
        info.handle = BASS_MIDI_FontInit_ptr(info.path.c_str(), 0x20000); // BASS_MIDI_FONT_MMAP
        info.loaded = (info.handle != 0);
    } else {
        std::cerr << "BASS_MIDI_FontInit function pointer not set" << std::endl;
        info.loaded = false;
    }
    
    if (info.loaded) {
        std::cout << "Loaded soundfont: " << info.name << " (handle: " << info.handle << ")" << std::endl;
    } else {
        std::cerr << "Failed to load soundfont: " << info.name << " from path: " << info.path << std::endl;
    }
    
    return info.loaded;
}

bool SoundFontManager::LoadSoundFont(const std::string& path) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        return LoadSoundFont(static_cast<size_t>(index));
    }
    return false;
}

bool SoundFontManager::LoadAllSoundFonts() {
    bool all_success = true;
    for (size_t i = 0; i < soundfonts_.size(); ++i) {
        // Only load if should_load is true
        if (soundfonts_[i].should_load) {
            if (!LoadSoundFont(i)) {
                all_success = false;
            }
        }
    }
    return all_success;
}

void SoundFontManager::UnloadSoundFont(size_t index) {
    if (index >= soundfonts_.size()) {
        return;
    }

    auto& info = soundfonts_[index];
    if (info.handle != 0 && BASS_MIDI_FontFree_ptr) {
        BASS_MIDI_FontFree_ptr(info.handle);
        info.handle = 0;
        info.loaded = false;
        std::cout << "Unloaded soundfont: " << info.name << std::endl;
    }
}

void SoundFontManager::UnloadSoundFont(const std::string& path) {
    int index = FindSoundFontIndex(path);
    if (index >= 0) {
        UnloadSoundFont(static_cast<size_t>(index));
    }
}

void SoundFontManager::UnloadAllSoundFonts() {
    for (size_t i = 0; i < soundfonts_.size(); ++i) {
        UnloadSoundFont(i);
    }
}

bool SoundFontManager::HasSoundFont(const std::string& path) const {
    return FindSoundFontIndex(path) >= 0;
}

int SoundFontManager::FindSoundFontIndex(const std::string& path) const {
    for (size_t i = 0; i < soundfonts_.size(); ++i) {
        if (soundfonts_[i].path == path) {
            return static_cast<int>(i);
        }
    }
    return -1;
}

bool SoundFontManager::ValidateSoundFontPath(const std::string& path) const {
    if (path.empty()) {
        return false;
    }
    
    // Check file extension
    if (!IsValidSoundFontExtension(path)) {
        return false;
    }
    
    // Check if file exists
    try {
        return std::filesystem::exists(path);
    } catch (const std::exception& e) {
        std::cerr << "Error checking soundfont path: " << e.what() << std::endl;
        return false;
    }
}

std::vector<std::string> SoundFontManager::SerializePaths() const {
    std::vector<std::string> paths;
    for (const auto& info : soundfonts_) {
        paths.push_back(info.path);
    }
    return paths;
}

std::vector<bool> SoundFontManager::SerializeEnabledStates() const {
    std::vector<bool> states;
    for (const auto& info : soundfonts_) {
        states.push_back(info.enabled);
    }
    return states;
}

std::vector<int> SoundFontManager::SerializePriorities() const {
    std::vector<int> priorities;
    for (const auto& info : soundfonts_) {
        priorities.push_back(info.priority);
    }
    return priorities;
}

std::vector<float> SoundFontManager::SerializeVolumes() const {
    std::vector<float> volumes;
    for (const auto& info : soundfonts_) {
        volumes.push_back(info.volume);
    }
    return volumes;
}

std::vector<bool> SoundFontManager::SerializeShouldLoadStates() const {
    std::vector<bool> should_load_states;
    for (const auto& info : soundfonts_) {
        should_load_states.push_back(info.should_load);
    }
    return should_load_states;
}

bool SoundFontManager::DeserializeFromConfig(const std::vector<std::string>& paths,
                                            const std::vector<bool>& enabled_states,
                                            const std::vector<int>& priorities,
                                            const std::vector<float>& volumes,
                                            const std::vector<bool>& should_load_states) {
    ClearAllSoundFonts();

    for (size_t i = 0; i < paths.size(); ++i) {
        SoundFontInfo info(paths[i], i);

        // Set enabled state if provided
        if (i < enabled_states.size()) {
            info.enabled = enabled_states[i];
        }

        // Set priority if provided
        if (i < priorities.size()) {
            info.priority = priorities[i];
        }

        // Set volume if provided
        if (i < volumes.size()) {
            info.volume = std::max(0.0f, std::min(1.0f, volumes[i])); // Clamp to valid range
        }

        // Set should_load state if provided
        if (i < should_load_states.size()) {
            info.should_load = should_load_states[i];
        }

        soundfonts_.push_back(info);
    }

    SortByPriority();
    NotifyChange();
    return true;
}

void SoundFontManager::NotifyChange() {
    if (change_callback_) {
        change_callback_();
    }
}

std::string SoundFontManager::ExtractFileName(const std::string& path) const {
    size_t last_slash = path.find_last_of("/\\");
    if (last_slash != std::string::npos) {
        return path.substr(last_slash + 1);
    }
    return path;
}

bool SoundFontManager::IsValidSoundFontExtension(const std::string& path) const {
    std::string lower_path = path;
    std::transform(lower_path.begin(), lower_path.end(), lower_path.begin(), ::tolower);

    // Check if path ends with valid soundfont extensions
    return (lower_path.length() >= 4 && lower_path.substr(lower_path.length() - 4) == ".sf2") ||
           (lower_path.length() >= 4 && lower_path.substr(lower_path.length() - 4) == ".sfz") ||
           (lower_path.length() >= 4 && lower_path.substr(lower_path.length() - 4) == ".sf3");
}

void SoundFontManager::UpdatePriorities() {
    for (size_t i = 0; i < soundfonts_.size(); ++i) {
        soundfonts_[i].priority = static_cast<int>(i);
    }
}
