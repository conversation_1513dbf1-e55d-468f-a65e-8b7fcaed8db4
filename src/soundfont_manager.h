#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>

// Include BASS headers for type definitions
#define NOBASSMIDIOVERLOADS
#include "bass.h"
#include "bassmidi.h"

struct SoundFontInfo {
    std::string path;           // Path to the soundfont file
    std::string name;           // Display name (derived from filename)
    bool enabled;               // Whether this soundfont is active
    int priority;               // Priority order (lower number = higher priority)
    float volume;               // Volume level (0.0 to 1.0, default 1.0 = 100%)
    bool should_load;           // Whether this soundfont should be loaded (user preference)
    HSOUNDFONT handle;          // BASS soundfont handle (0 if not loaded)
    bool loaded;                // Whether the soundfont is successfully loaded

    SoundFontInfo() : enabled(true), priority(0), volume(1.0f), should_load(true), handle(0), loaded(false) {}

    SoundFontInfo(const std::string& file_path, int prio = 0)
        : path(file_path), enabled(true), priority(prio), volume(1.0f), should_load(true), handle(0), loaded(false) {
        // Extract filename from path for display name
        size_t last_slash = file_path.find_last_of("/\\");
        if (last_slash != std::string::npos) {
            name = file_path.substr(last_slash + 1);
        } else {
            name = file_path;
        }
    }
};

class SoundFontManager {
public:
    SoundFontManager();
    ~SoundFontManager();

    // Soundfont management
    bool AddSoundFont(const std::string& path);
    bool RemoveSoundFont(const std::string& path);
    bool RemoveSoundFont(size_t index);
    void ClearAllSoundFonts();
    
    // Enable/disable soundfonts
    bool SetSoundFontEnabled(size_t index, bool enabled);
    bool SetSoundFontEnabled(const std::string& path, bool enabled);

    // Volume control
    bool SetSoundFontVolume(size_t index, float volume);
    bool SetSoundFontVolume(const std::string& path, float volume);
    float GetSoundFontVolume(size_t index) const;
    float GetSoundFontVolume(const std::string& path) const;

    // Load state control
    bool SetSoundFontShouldLoad(size_t index, bool should_load);
    bool SetSoundFontShouldLoad(const std::string& path, bool should_load);
    bool GetSoundFontShouldLoad(size_t index) const;
    bool GetSoundFontShouldLoad(const std::string& path) const;
    
    // Priority management
    bool SetSoundFontPriority(size_t index, int priority);
    bool MoveSoundFontUp(size_t index);
    bool MoveSoundFontDown(size_t index);
    void SortByPriority();
    
    // Access methods
    const std::vector<SoundFontInfo>& GetSoundFonts() const { return soundfonts_; }
    size_t GetSoundFontCount() const { return soundfonts_.size(); }
    bool HasSoundFonts() const { return !soundfonts_.empty(); }
    bool HasEnabledSoundFonts() const;
    
    // Get enabled soundfonts in priority order
    std::vector<const SoundFontInfo*> GetEnabledSoundFonts() const;
    
    // Load/unload soundfonts using BASS
    bool LoadSoundFont(size_t index);
    bool LoadSoundFont(const std::string& path);
    bool LoadAllSoundFonts();
    void UnloadSoundFont(size_t index);
    void UnloadSoundFont(const std::string& path);
    void UnloadAllSoundFonts();
    
    // Check if a soundfont exists
    bool HasSoundFont(const std::string& path) const;
    int FindSoundFontIndex(const std::string& path) const;
    
    // Validation
    bool ValidateSoundFontPath(const std::string& path) const;
    
    // Serialization for config
    std::vector<std::string> SerializePaths() const;
    std::vector<bool> SerializeEnabledStates() const;
    std::vector<int> SerializePriorities() const;
    std::vector<float> SerializeVolumes() const;
    std::vector<bool> SerializeShouldLoadStates() const;
    
    bool DeserializeFromConfig(const std::vector<std::string>& paths,
                              const std::vector<bool>& enabled_states = {},
                              const std::vector<int>& priorities = {},
                              const std::vector<float>& volumes = {},
                              const std::vector<bool>& should_load_states = {});
    
    // Callback for when soundfonts change (for UI updates)
    void SetChangeCallback(std::function<void()> callback) { change_callback_ = callback; }

    // Set BASS function pointers (called by AudioEngine)
    void SetBASSFunctionPointers(HSOUNDFONT (*font_init_ptr)(const void*, DWORD),
                                BOOL (*font_free_ptr)(HSOUNDFONT)) {
        BASS_MIDI_FontInit_ptr = font_init_ptr;
        BASS_MIDI_FontFree_ptr = font_free_ptr;
    }

private:
    std::vector<SoundFontInfo> soundfonts_;
    std::function<void()> change_callback_;
    
    // BASS function pointers (will be set by AudioEngine)
    HSOUNDFONT (*BASS_MIDI_FontInit_ptr)(const void* file, DWORD flags);
    BOOL (*BASS_MIDI_FontFree_ptr)(HSOUNDFONT handle);
    
    // Helper methods
    void NotifyChange();
    std::string ExtractFileName(const std::string& path) const;
    bool IsValidSoundFontExtension(const std::string& path) const;
    void UpdatePriorities();
    
    friend class AudioEngine; // Allow AudioEngine to set function pointers
};
