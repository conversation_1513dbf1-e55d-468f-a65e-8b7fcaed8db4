#include "note_indicator.h"
#include <algorithm>
#include <cmath>

#ifndef M_PI
#define M_PI 3.14159265358979323846
#endif

NoteIndicator::NoteIndicator()
    : position_(10.0f, 10.0f)  // Default top-left position
    , size_(80.0f, 30.0f)     // Default size
    , original_position_(10.0f, 10.0f)
    , color_(0.2f, 0.6f, 1.0f, 0.8f)  // Default blue color with transparency
    , enabled_(true)
    , is_animating_(false)
    , animation_start_time_(std::chrono::steady_clock::now())
    , animation_duration_ms_(200.0f)  // 200ms animation
    , bounce_distance_(8.0f)          // 8 pixels down
    , animation_progress_(0.0f)
{
}

NoteIndicator::~NoteIndicator() {
}

void NoteIndicator::Initialize() {
    original_position_ = position_;
    is_animating_ = false;
    animation_progress_ = 0.0f;
}

void NoteIndicator::Update() {
    if (is_animating_) {
        UpdateAnimation();
    }
}

void NoteIndicator::Render(OpenGLRenderer& renderer) {
    if (!enabled_) {
        return;
    }

    // Calculate current position based on animation
    Vec2 current_position = CalculateAnimatedPosition();
    
    // Render the indicator as a rounded rectangle
    renderer.DrawRectGradientRounded(
        current_position,
        size_,
        Color(color_.r, color_.g, color_.b, color_.a * 0.9f),  // Slightly more opaque at top
        Color(color_.r, color_.g, color_.b, color_.a * 0.7f),  // Slightly more transparent at bottom
        5.0f  // Corner radius
    );
    
    // Add a subtle border
    renderer.DrawRectWithRoundedBorder(
        current_position,
        size_,
        Color(0.0f, 0.0f, 0.0f, 0.0f),  // Transparent fill (already drawn above)
        Color(1.0f, 1.0f, 1.0f, color_.a * 0.3f),  // White border with low opacity
        1.0f,  // Border width
        5.0f   // Corner radius
    );
}

void NoteIndicator::TriggerAnimation() {
    if (!enabled_) {
        return;
    }
    
    is_animating_ = true;
    animation_start_time_ = std::chrono::steady_clock::now();
    animation_progress_ = 0.0f;
}

void NoteIndicator::SetPosition(const Vec2& position) {
    position_ = position;
    original_position_ = position;
}

void NoteIndicator::SetSize(const Vec2& size) {
    size_ = size;
}

void NoteIndicator::SetColor(const Color& color) {
    color_ = color;
}

void NoteIndicator::SetEnabled(bool enabled) {
    enabled_ = enabled;
    if (!enabled) {
        is_animating_ = false;
        animation_progress_ = 0.0f;
    }
}

void NoteIndicator::SetAnimationDuration(float duration_ms) {
    animation_duration_ms_ = duration_ms;
}

void NoteIndicator::SetBounceDistance(float distance) {
    bounce_distance_ = distance;
}

bool NoteIndicator::IsAnimating() const {
    return is_animating_;
}

bool NoteIndicator::IsEnabled() const {
    return enabled_;
}

void NoteIndicator::UpdateAnimation() {
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - animation_start_time_);
    
    animation_progress_ = elapsed.count() / animation_duration_ms_;
    
    if (animation_progress_ >= 1.0f) {
        // Animation complete
        is_animating_ = false;
        animation_progress_ = 0.0f;
    }
}

Vec2 NoteIndicator::CalculateAnimatedPosition() const {
    if (!is_animating_ || animation_progress_ <= 0.0f) {
        return original_position_;
    }
    
    // Create a bounce effect: down and then back up
    // Use a sine wave for smooth motion
    float eased_progress = EaseInOutQuad(animation_progress_);
    float bounce_offset = std::sin(eased_progress * M_PI) * bounce_distance_;
    
    return Vec2(original_position_.x, original_position_.y + bounce_offset);
}

float NoteIndicator::EaseInOutQuad(float t) const {
    // Quadratic easing function for smooth animation
    if (t < 0.5f) {
        return 2.0f * t * t;
    } else {
        return -1.0f + (4.0f - 2.0f * t) * t;
    }
}
