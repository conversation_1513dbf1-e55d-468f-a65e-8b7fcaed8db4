#pragma once

#include <chrono>
#include <vector>
#include <deque>
#include <mutex>

class NoteStatistics {
public:
    NoteStatistics();
    ~NoteStatistics();

    // Initialize the statistics system
    void Initialize();

    // Update statistics (should be called regularly, e.g., every frame)
    void Update();

    // Record a note being played
    void RecordNotePlayed();

    // Get total notes played since start
    int GetTotalNotesPlayed() const;

    // Get current Notes Per Second
    float GetCurrentNPS() const;

    // Get NPS history for graphing (returns array of recent NPS values)
    const std::vector<float>& GetNPSHistory() const;

    // Get maximum NPS recorded
    float GetMaxNPS() const;

    // Get average NPS over the session
    float GetAverageNPS() const;

    // Reset all statistics
    void Reset();

    // Configuration
    void SetHistorySize(int size);
    void SetUpdateInterval(float interval_seconds);

private:
    // Total notes played since initialization
    int total_notes_played_;

    // Current NPS calculation
    float current_nps_;
    float max_nps_;
    float average_nps_;

    // Time tracking
    std::chrono::steady_clock::time_point start_time_;
    std::chrono::steady_clock::time_point last_update_time_;
    std::chrono::steady_clock::time_point last_nps_calculation_time_;

    // Note timing for NPS calculation
    std::deque<std::chrono::steady_clock::time_point> recent_note_times_;

    // History for graphing
    std::vector<float> nps_history_;
    int max_history_size_;

    // Update interval for NPS calculation
    float update_interval_seconds_;

    // Thread safety
    mutable std::mutex statistics_mutex_;

    // Helper methods
    void CalculateNPS();
    void UpdateHistory();
    void CleanOldNotes();
};
