#pragma once

#include <vector>
#include <cmath>
#include <stdexcept>
#include <array>

// 色を表現するための単純な構造体
struct RainbowColor {
    unsigned char r = 0;
    unsigned char g = 0;
    unsigned char b = 0;

    RainbowColor() = default;
    RainbowColor(unsigned char red, unsigned char green, unsigned char blue)
        : r(red), g(green), b(blue) {}
};

// 虹色を生成する機能を提供するクラス
class Rainbow {
public:
    /**
     * @brief Rainbowオブジェクトのコンストラクタ
     * @param numberOfColors 虹色のサイクルごとの色の数。正の数でなければなりません。
     */
    explicit Rainbow(int numberOfColors) {
        if (numberOfColors <= 0) {
            throw std::invalid_argument("Number of colors must be positive.");
        }
        numberOfColors_ = numberOfColors;
        colorIndex_ = 0;
    }

    /**
     * @brief サイクル内の次の虹色を取得します。
     * @return 次のRainbowColorオブジェクト。
     */
    RainbowColor next() {
        // 現在のインデックスに基づいて正規化されたインデックス(0-1)を計算
        float normalizedIndex = static_cast<float>(colorIndex_) / numberOfColors_;
        // 次の呼び出しのためにインデックスを更新（サイクルさせる）
        colorIndex_ = (colorIndex_ + 1) % numberOfColors_;
        return getColor(normalizedIndex);
    }

    /**
     * @brief 指定された数の虹色の配列（ベクター）を生成します。
     * @param numberOfColors 生成する色の数。非負でなければなりません。
     * @return RainbowColorオブジェクトのstd::vector。
     */
    static std::vector<RainbowColor> create(int numberOfColors) {
        if (numberOfColors < 0) {
            throw std::invalid_argument("Number of colors must be non-negative.");
        }
        std::vector<RainbowColor> rainbow;
        if (numberOfColors == 0) {
            return rainbow;
        }
        rainbow.reserve(numberOfColors);
        for (int i = 0; i < numberOfColors; ++i) {
            rainbow.push_back(getColor(static_cast<float>(i) / numberOfColors));
        }
        return rainbow;
    }

private:
    // 定数
    static constexpr int MAX_RGB = 255;
    static constexpr int NUM_SECTIONS = 6;

    // メンバ変数
    int numberOfColors_;
    int colorIndex_;

    /**
     * @brief 正規化されたインデックス(0-1)に基づいて虹色を取得します。
     * @param index 0から1の間の正規化されたインデックス。
     * @return 計算されたRainbowColorオブジェクト。
     */
    static RainbowColor getColor(float index) {
        if (index < 0.0f) index = 0.0f;
        if (index > 1.0f) index = 1.0f;

        int section = static_cast<int>(std::floor(index * NUM_SECTIONS));
        // 最後のセクションが範囲外になるのを防ぐ (indexが1.0の場合)
        if (section >= NUM_SECTIONS) {
          section = NUM_SECTIONS - 1;
        }

        float start = (index - (static_cast<float>(section) / NUM_SECTIONS)) * NUM_SECTIONS;
        float end = 1.0f - start;

        std::array<float, 3> colorArray{}; // 0-1の範囲のRGBカラー配列

        switch (section) {
            case 0: // 赤 -> 黄
                colorArray = {1.0f, start, 0.0f};
                break;
            case 1: // 黄 -> 緑
                colorArray = {end, 1.0f, 0.0f};
                break;
            case 2: // 緑 -> シアン
                colorArray = {0.0f, 1.0f, start};
                break;
            case 3: // シアン -> 青
                colorArray = {0.0f, end, 1.0f};
                break;
            case 4: // 青 -> マゼンタ
                colorArray = {start, 0.0f, 1.0f};
                break;
            case 5: // マゼンタ -> 赤
                colorArray = {1.0f, 0.0f, end};
                break;
        }

        // RainbowColorオブジェクトを返す
        return RainbowColor(
            static_cast<unsigned char>(colorArray[0] * MAX_RGB),
            static_cast<unsigned char>(colorArray[1] * MAX_RGB),
            static_cast<unsigned char>(colorArray[2] * MAX_RGB)
        );
    }
};
