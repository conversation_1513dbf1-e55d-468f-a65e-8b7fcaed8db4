#pragma once

#include <chrono>
#include "opengl_renderer.h"

class NoteIndicator {
public:
    NoteIndicator();
    ~NoteIndicator();

    // Initialize the indicator
    void Initialize();

    // Update animation state
    void Update();

    // Render the indicator
    void Render(OpenGLRenderer& renderer);

    // Trigger the bounce animation when a note is played
    void TriggerAnimation();

    // Configuration
    void SetPosition(const Vec2& position);
    void SetSize(const Vec2& size);
    void SetColor(const Color& color);
    void SetEnabled(bool enabled);
    
    // Animation settings
    void SetAnimationDuration(float duration_ms);
    void SetBounceDistance(float distance);
    
    // Get current state
    bool IsAnimating() const;
    bool IsEnabled() const;

private:
    // Position and size
    Vec2 position_;
    Vec2 size_;
    Vec2 original_position_;
    
    // Appearance
    Color color_;
    bool enabled_;
    
    // Animation state
    bool is_animating_;
    std::chrono::steady_clock::time_point animation_start_time_;
    float animation_duration_ms_;
    float bounce_distance_;
    float animation_progress_;
    
    // Helper functions
    void UpdateAnimation();
    Vec2 CalculateAnimatedPosition() const;
    float EaseInOutQuad(float t) const;
};
